import { Nango } from '@nangohq/node';
import { SyncSuccessPayload } from './sync-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { triggerTaskFlow } from '../_taskflow';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import { debug } from '../_shared/debug';
import { matchCondition } from '../_taskflow/jsonLogicUtils';

/**
 * Handles incoming Nango webhook requests for data sync events.
 */
export async function handleDataSync(
  body: SyncSuccessPayload,
  supabase: SupabaseClient,
  nango: Nango
): Promise<number> {
  const supabaseFacade = new TaskflowSupabaseFacade(supabase);
  // Normalize responseResults
  const results = Array.isArray(body.responseResults)
    ? {
        added: body.responseResults[0],
        updated: body.responseResults[1],
        deleted: body.responseResults[2],
      }
    : body.responseResults;

  if (results.added === 0 && results.updated === 0 && results.deleted === 0) {
    return 200;
  }

  const { data: connection, error: getConnectionError } = await supabase
    .from('connections')
    .select(`id, userId, providerKey`)
    .eq('id', body.connectionId)
    .single();

  if (getConnectionError) {
    return 404;
  }

  const { userId } = connection;

  const { data: syncTriggers, error: syncTriggersError } = await supabase
    .from('sync_triggers')
    .select(
      `
        id,
        taskflowId,
        providerKey,
        model,
        syncKey,
        cursor,
        condition,
        taskflows!inner(id, conversationId, active, conversations!taskflows_conversationId_fkey!inner(userId))
      `
    )
    .eq('providerKey', body.providerConfigKey)
    .eq('model', body.model)
    .eq('taskflows.conversations.userId', userId)
    .eq('taskflows.active', true);

  if (syncTriggersError) {
    debug(`[handleDataSync] Error fetching sync triggers: ${JSON.stringify(syncTriggersError)}`);
    return 500;
  }

  debug(`[handleDataSync] sync triggers ${JSON.stringify(syncTriggers)}`);

  if (!syncTriggers || syncTriggers.length === 0) {
    debug(`[handleDataSync] No sync triggers found for model ${JSON.stringify(syncTriggers)}`);
    return 200;
  }

  const result = await nango.listRecords({
    providerConfigKey: body.providerConfigKey,
    connectionId: body.connectionId,
    model: body.model,
    modifiedAfter: body.modifiedAfter,
  });

  if (!result?.records?.length) {
    debug(`[handleDataSync] No records found for model ${body.model}`);
    return 200;
  }

  let successCount = 0;
  let errorCount = 0;

  debug(`[handleDataSync] records ${JSON.stringify(result.records, null, 2)}`);

  for (const syncTrigger of syncTriggers) {
    const records = result.records.filter(record => {
      if (!matchCondition(record, syncTrigger.condition)) {
        debug(
          `Record ${JSON.stringify(record)} does not match condition ${JSON.stringify(
            syncTrigger.condition
          )}`
        );
        return false;
      }
      return true;
    });

    for (const record of records) {
      try {
        const success = await triggerTaskFlow({
          taskflowId: syncTrigger.taskflowId,
          triggerData: record,
          supabaseFacade,
          userId,
        });

        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        errorCount++;
      }
    }
  }

  return 200;
}

