import { initNango } from '../_shared/nango';
import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { debug } from '../_shared/debug';
import { createExecution, triggerWithExecution } from '../_taskflow/index';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import { matchCondition } from '../_taskflow/jsonLogicUtils';
import type { Context } from '@netlify/functions';

const DEFAULT_RECORD_LIMIT = 10;

interface TestWorkflowRequest {
  taskflowId: string;
  modelIds?: string[];
  filter?: any;
}

const nango = initNango();

export default async function handler(req: Request, context: Context) {
  try {
    // Handle CORS
    debug('Handling CORS');
    const corsResponse = handleCors(req);
    if (corsResponse) {
      debug('CORS response:', corsResponse);
      return corsResponse;
    }

    // Validate request method
    debug('Validating request method');
    if (req.method !== 'POST') {
      return validationError('Method not allowed');
    }

    // Parse request body
    debug('Parsing request body');
    const { taskflowId, modelIds, filter } = (await req.json()) as TestWorkflowRequest;

    debug(
      'Parsed request body:',
      `taskflowId: ${taskflowId}`,
      `modelIds: ${modelIds}`,
      `filter: ${filter}`
    );

    if (!taskflowId) {
      return validationError('Taskflow ID is required');
    }

    // Authenticate request and get Supabase clients
    debug('Authenticating request and getting Supabase clients');
    const authResult = await initAuthenticate(req);
    if (authResult.length === 1) {
      debug('Authentication error:', authResult[0]);
      return authResult[0];
    }

    const [, user, supabaseAdmin, supabaseUser] = authResult;

    // Get the taskflow
    debug('Getting taskflow');
    const { data: taskflow, error: taskflowError } = await supabaseUser
      .from('taskflows')
      .select('id, schema')
      .eq('id', taskflowId)
      .single();

    if (taskflowError || !taskflow) {
      return errorResponse(`Taskflow not found: ${taskflowError?.message}`, 404);
    }

    // Get the trigger node from the taskflow schema
    debug('Getting trigger node from schema');
    const triggerNode = taskflow.schema?.triggers?.[0];

    if (!triggerNode) {
      return errorResponse('No trigger node found in taskflow schema', 400);
    }

    // Extract provider information from the trigger node
    debug('Extracting provider information');
    const providerKey = triggerNode.parameters?.providerKey;
    const model = triggerNode.parameters?.model;
    const syncKey = triggerNode.parameters?.syncKey;

    if (!providerKey) {
      return errorResponse('Trigger node missing providerKey', 400);
    }

    // Get the connection for the provider
    debug('Getting connection for provider:', providerKey);
    const { data: connection, error: connectionError } = await supabaseUser
      .from('connections')
      .select('id')
      .eq('providerKey', providerKey)
      .single();

    if (connectionError || !connection) {
      return errorResponse(`No connection found for provider: ${connectionError?.message}`, 404);
    }

    // Get test data from Nango
    debug('Getting test data from Nango');
    let testRecord;
    try {
      const result = await nango.listRecords({
        providerConfigKey: providerKey,
        connectionId: connection.id,
        model,
        limit: DEFAULT_RECORD_LIMIT,
        ...(modelIds && { ids: modelIds }),
        ...(filter && { filter }),
      });

      debug('Nango listRecords result:', result);

      if (!result.records || result.records.length === 0) {
        return errorResponse('No records found to test with', 404);
      }

      testRecord = result.records.find(record => matchCondition(record, filter));

      if (!testRecord) {
        return errorResponse('No records found matching the filter', 404);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return errorResponse(`Failed to get test data: ${errorMessage}`, 500);
    }

    // Create a test execution
    debug('Creating test execution');

    // Create the execution using the taskflow function
    const supabaseFacade = new TaskflowSupabaseFacade(supabaseAdmin);

    const [preparedData, error] = await createExecution({
      taskflowId: taskflow.id,
      userId: user.id,
      triggerData: testRecord,
      supabaseFacade,
    });

    if (error) {
      return errorResponse(`Failed to create execution: ${error.message}`, 500);
    }

    const taskflowExecutionId = preparedData?.taskflowExecution?.id;

    if (!taskflowExecutionId) {
      return errorResponse('Failed to create execution: No execution data returned', 500);
    }

    // Prepare response and schedule background tasks
    const response = new Response(JSON.stringify({ executionId: taskflowExecutionId }), {
      headers: corsHeaders,
      status: 200,
    });
    // Perform update and trigger in background
    context.waitUntil(
      (async () => {
        debug('Updating taskflow with test execution ID');
        const { error: updateError } = await supabaseAdmin
          .from('taskflows')
          .update({ testExecutionId: taskflowExecutionId })
          .eq('id', taskflow.id);
        if (updateError)
          debug('Error updating taskflow with test execution ID:', updateError);
        debug('Triggering execution as side effect');
        try {
          await triggerWithExecution({
            taskflowExecutionId,
            userId: user.id,
            supabaseFacade,
          });
        } catch (error: any) {
          debug('Error triggering execution:', error);
        }
      })()
    );
    return response;
  } catch (error) {
    debug('Error in handler:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return errorResponse(`Internal error: ${errorMessage}`, 500);
  }
}

