import React, { useState, useRef, useEffect } from 'react';
import { Folder, ExternalLink, MoreHorizontal } from 'lucide-react';
import { DropboxFolder } from 'src/config/nangoModels';

interface DropboxFolderDisplayProps {
  output: DropboxFolder;
}

/**
 * Renders a rich display of a Dropbox folder with context menu linking
 */
function DropboxFolderDisplay({ output }: DropboxFolderDisplayProps) {
  const folder = output;
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const contextMenuRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(false);
      }
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Check if we have valid data
  if (!folder) {
    return (
      <div className="p-6 text-center">
        <Folder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No folder data available</p>
      </div>
    );
  }

  // Handle right click for context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  };

  // Dropbox link URL
  const dropboxUrl = `https://www.dropbox.com/home${folder.path_display}`;

  return (
    <div className="relative">
      {/* Context Menu */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-[140px]"
          style={{ left: contextMenuPosition.x, top: contextMenuPosition.y }}
        >
          <a
            href={dropboxUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={() => setShowContextMenu(false)}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            View in Dropbox
          </a>
        </div>
      )}

      <div onContextMenu={handleContextMenu}>
        {/* Header */}
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Dropbox Folder</h3>
          </div>
        </div>

        <div className="p-5 space-y-4">
          {/* Folder name */}
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
              <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-base font-medium text-gray-900 dark:text-white flex-1">{folder.name}</h4>
                <div className="flex items-center">
                  <div className="relative">
                    <button 
                      type="button" 
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      onClick={() => setShowDropdown(!showDropdown)}
                    >
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                    {showDropdown && (
                      <div 
                        ref={dropdownRef}
                        className="absolute z-50 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-[140px]"
                      >
                        <a
                          href={dropboxUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => setShowDropdown(false)}
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View in Dropbox
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Path */}
          {folder.path_display && (
            <div className="mt-3">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Path:</div>
              <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
                {folder.path_display}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { DropboxFolderDisplay };
