import React, { useState, useRef, useEffect } from 'react';
import { FileText, Calendar, Download, ExternalLink, MoreHorizontal } from 'lucide-react';
import { format } from 'date-fns';
import { DropboxFile } from 'src/config/nangoModels';

interface DropboxFileDisplayProps {
  output: DropboxFile;
}

/**
 * Renders a rich display of a Dropbox file with context menu linking
 */
function DropboxFileDisplay({ output }: DropboxFileDisplayProps) {
  const file = output;
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const contextMenuRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(false);
      }
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Check if we have valid data
  if (!file) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No file data available</p>
      </div>
    );
  }

  // Format date if available
  let formattedDate = '';
  if (file.server_modified) {
    try {
      formattedDate = format(new Date(file.server_modified), 'MMM d, yyyy h:mm a');
    } catch (e) {
      formattedDate = file.server_modified;
    }
  }

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Determine file icon based on mime type
  const getFileIcon = () => {
    return <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  };

  // Handle right click for context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  };

  // Dropbox link URL
  const dropboxUrl = `https://www.dropbox.com/home${file.path_display}`;

  return (
    <div className="relative">
      {/* Context Menu */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-[140px]"
          style={{ left: contextMenuPosition.x, top: contextMenuPosition.y }}
        >
          <a
            href={dropboxUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={() => setShowContextMenu(false)}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            View in Dropbox
          </a>
          {file.download_url && (
            <a
              href={file.download_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              onClick={() => setShowContextMenu(false)}
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </a>
          )}
        </div>
      )}

      <div onContextMenu={handleContextMenu}>
        {/* Header with UX Approach 2: Three-dot dropdown in title bar */}
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">Dropbox File</h3>
            </div>

            {/* UX Approach 2: Three-dot menu */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                title="More options"
              >
                <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </button>

              {showDropdown && (
                <div className="absolute right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-[140px] z-10">
                  <a
                    href={dropboxUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => setShowDropdown(false)}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View in Dropbox
                  </a>
                  {file.download_url && (
                    <a
                      href={file.download_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => setShowDropdown(false)}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-5 space-y-4">
          {/* File name and info */}
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
              {getFileIcon()}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-base font-medium text-gray-900 dark:text-white flex-1">
                  {file.name}
                </h4>
                {file.download_url && (
                  <a
                    href={file.download_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <Download className="w-5 h-5" />
                  </a>
                )}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                {formatFileSize(file.size)}
              </p>
            </div>
          </div>

          {/* Path */}
          {file.path_display && (
            <div className="mt-3">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Path:</div>
              <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
                {file.path_display}
              </div>
            </div>
          )}

          {/* Last modified */}
          {formattedDate && (
            <div className="flex items-start mt-3">
              <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Modified:</div>
                <div className="text-sm text-gray-800 dark:text-gray-200">{formattedDate}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { DropboxFileDisplay };
