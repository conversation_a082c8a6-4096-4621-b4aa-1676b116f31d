import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runNotionTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('notion', [
    { actionKey: 'search', params: { query: '' } },
  ], onProgress);

  const searchResults = (results[0].output as any)?.results;
  const parentPage = searchResults?.find((result: any) => result.object === 'page');

  if (parentPage) {
    const more = await executeProviderActions('notion', [
      { actionKey: 'get-page', params: { pageId: parentPage.id } },
      { 
        actionKey: 'create-page', 
        params: { 
          parent: { page_id: parentPage.id },
          properties: {
            title: [
              {
                text: {
                  content: 'ActionsInspector Test Page'
                }
              }
            ]
          }
        } 
      },
    ], onProgress);
    results.push(...more);

    const newPageId = (more[1].output as any)?.id;
    if (newPageId) {
      const final = await executeProviderActions('notion', [
        {
          actionKey: 'update-page',
          params: {
            pageId: newPageId,
            properties: {
              title: [
                {
                  text: {
                    content: 'ActionsInspector Test Page (Updated)'
                  }
                }
              ]
            }
          } 
        },
      ], onProgress);
      results.push(...final);
    }
  }
  await saveScriptResult('notion', results);
  return results;
}
