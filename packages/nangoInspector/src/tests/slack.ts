import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runSlackTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('slack', [
    { actionKey: 'list-channels', params: {} },
  ], onProgress);

  const channelId = (results[0].output as any)?.channels?.[0]?.id;
  if (!channelId) return results;

  const more = await executeProviderActions('slack', [
    { actionKey: 'send-message-as-user', params: { channel: channelId, text: 'Hello from ActionsInspector' }, filename: 'send-message-as-user' },
  ], onProgress);
  results.push(...more);
  const ts = (more[0].output as any)?.ts;
  if (!ts) return results;

  const final = await executeProviderActions('slack', [
    { actionKey: 'update-message-as-user', params: { channel: channelId, ts, text: 'Updated via ActionsInspector' } },
    { actionKey: 'add-reaction-as-user', params: { channel: channelId, timestamp: ts, name: 'thumbsup' } },
  ], onProgress);
  results.push(...final);
  await saveScriptResult('slack', results);
  return results;
}
