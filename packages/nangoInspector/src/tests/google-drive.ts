import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleDriveTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-drive', [
    { actionKey: 'list-root-folders', params: {} },
  ], onProgress);

  const folderId = (results[0].output as any)?.folders?.[0]?.id;

  const more = await executeProviderActions('google-drive', [
    { actionKey: 'list-documents', params: folderId ? { folderId } : {} },
  ], onProgress);

  results.push(...more);
  await saveScriptResult('google-drive', results);
  return results;
}
