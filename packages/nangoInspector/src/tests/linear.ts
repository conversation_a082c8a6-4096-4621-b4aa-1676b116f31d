import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runLinearTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('linear', [
    { actionKey: 'list-teams', params: {} },
  ], onProgress);

  const teams = (results[0].output as any)?.teams;
  const firstTeam = teams?.[0];

  if (firstTeam) {
    const more = await executeProviderActions('linear', [
      { actionKey: 'get-team', params: { id: firstTeam.id } },
      { actionKey: 'list-issues', params: { teamId: firstTeam.id } },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('linear', [
      { 
        actionKey: 'create-issue', 
        params: { 
          teamId: firstTeam.id,
          title: 'ActionsInspector Test Issue',
          description: 'This is a test issue created by ActionsInspector'
        } 
      },
    ], onProgress);
    results.push(...final);

    const issueId = (final[0].output as any)?.id;
    if (issueId) {
      const updateIssue = await executeProviderActions('linear', [
        {
          actionKey: 'update-issue',
          params: {
            id: issueId,
            title: 'ActionsInspector Test Issue (Updated)'
          }
        },
      ], onProgress);
      results.push(...updateIssue);
    }
  }
  await saveScriptResult('linear', results);
  return results;
}
