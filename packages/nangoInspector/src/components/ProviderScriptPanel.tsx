import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>cle, XCircle, Clock } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ProviderScriptPanelProps {
  provider: string;
  onExecute: (provider: string) => void;
  isExecuting: boolean;
  initialSteps?: StepResult[] | null;
}

interface StepResult {
  action: string;
  input: any;
  output?: any;
  valid?: boolean;
  error?: string;
  status: 'pending' | 'running' | 'completed' | 'error';
}

export function ProviderScriptPanel({ provider, onExecute, isExecuting, initialSteps = [] }: ProviderScriptPanelProps) {
  const mapInitial = (s: StepResult): StepResult => ({
    ...s,
    status: s.error ? 'error' : s.output ? 'completed' : 'pending'
  });

  const [steps, setSteps] = useState<StepResult[]>(() => (initialSteps || []).map(mapInitial));
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  useEffect(() => {
    // Only clear steps and currentStep when starting a new execution
    if (isExecuting) {
      // Set up EventSource for real-time updates
      const eventSource = new EventSource(`/api/run-stream/${provider}`);

      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        switch (data.type) {
          case 'start':
            setSteps([]);
            setCurrentStep(null);
            break;
          case 'progress':
            const step = data.step;
            setCurrentStep(step.action);
            setSteps(prev => {
              const existingIndex = prev.findIndex(s => s.action === step.action);
              const status: StepResult['status'] = step.error
                ? 'error'
                : step.output
                ? 'completed'
                : 'running';
              const newStep: StepResult = {
                action: step.action,
                input: step.input,
                output: step.output,
                valid: step.valid,
                error: step.error,
                status,
              };
              if (existingIndex >= 0) {
                const updated = [...prev];
                updated[existingIndex] = newStep;
                return updated;
              } else {
                return [...prev, newStep];
              }
            });
            break;
          case 'complete':
            setCurrentStep(null);
            break;
          case 'error':
            setCurrentStep(null);
            console.error('Provider script error:', data.error);
            break;
        }
      };

      eventSource.onerror = () => {
        eventSource.close();
        setCurrentStep(null);
      };

      return () => {
        eventSource.close();
      };
    }
    // Do NOT clear steps or currentStep when isExecuting becomes false
    // Only clear when starting a new execution (above)
  }, [provider, isExecuting]);

  useEffect(() => {
    if (!isExecuting) {
      setSteps((initialSteps || []).map(mapInitial));
    }
  }, [initialSteps, isExecuting, provider]);

  const getStepIcon = (step: StepResult) => {
    switch (step.status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getProviderDescription = (provider: string) => {
    switch (provider) {
      case 'slack':
        return 'Tests Slack integration by listing channels, sending a message, updating it, and adding a reaction.';
      case 'google-drive':
        return 'Tests Google Drive integration by listing root folders and documents.';
      case 'dropbox':
        return 'Tests Dropbox integration by listing files, creating folders, uploading files, and copying files.';
      case 'github':
        return 'Tests GitHub integration by listing repositories, issues, pull requests, and branches.';
      case 'google-calendar':
        return 'Tests Google Calendar integration by listing calendars, events, and creating test events.';
      case 'google-docs':
        return 'Tests Google Docs integration by creating documents, updating content, and fetching document data.';
      case 'google-mail':
        return 'Tests Gmail integration by listing messages, getting message details, and creating drafts.';
      case 'google-sheet':
        return 'Tests Google Sheets integration by creating spreadsheets, adding data, and updating cells.';
      case 'harvest':
        return 'Tests Harvest integration by listing clients, projects, tasks, and managing time entries.';
      case 'linear':
        return 'Tests Linear integration by listing teams, issues, creating new issues, and updating them.';
      case 'linkedin':
        return 'Tests LinkedIn integration by getting user profile and creating posts.';
      case 'notion':
        return 'Tests Notion integration by searching pages, creating new pages, and updating page properties.';
      case 'x-social':
      case 'twitter-v2':
        return 'Tests X (Twitter) integration by getting user profile and sending posts.';
      default:
        return `Tests ${provider} integration with a sequence of related actions.`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Provider Script Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="text-2xl">🚀</span>
            <span>{provider} Provider Script</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {getProviderDescription(provider)}
          </p>

          <Button
            onClick={() => onExecute(provider)}
            disabled={isExecuting}
            className="w-full"
          >
            {isExecuting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Script...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Run Provider Script
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Execution Steps */}
      {steps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Execution Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {steps.map((step, index) => (
                <div
                  key={`${step.action}-${index}`}
                  className={cn(
                    "flex items-start space-x-3 p-3 rounded-lg border",
                    step.status === 'running' && "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",
                    step.status === 'completed' && "bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800",
                    step.status === 'error' && "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800",
                    step.status === 'pending' && "bg-gray-50 dark:bg-gray-950/20 border-gray-200 dark:border-gray-800"
                  )}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {getStepIcon(step)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{step.action}</span>
                      {step.status === 'running' && (
                        <span className="text-xs text-blue-600 dark:text-blue-400">Running...</span>
                      )}
                    </div>

                    {step.input && Object.keys(step.input).length > 0 && (
                      <div className="mt-1">
                        <span className="text-xs text-muted-foreground">Input: </span>
                        <code className="text-xs bg-muted px-1 rounded">
                          {JSON.stringify(step.input)}
                        </code>
                      </div>
                    )}

                    {step.error && (
                      <div className="mt-1 text-xs text-red-600 dark:text-red-400">
                        Error: {step.error}
                      </div>
                    )}

                    {step.output && (
                      <details className="mt-2">
                        <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                          View output
                        </summary>
                        <pre className="mt-1 text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(step.output, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
