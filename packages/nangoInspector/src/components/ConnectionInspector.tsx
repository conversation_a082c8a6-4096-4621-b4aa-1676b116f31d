import { useState, useEffect } from 'react';
import {
  ChevronDown,
  Database,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  Pause,
  Play,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON>mmer, ShimmerCard, ShimmerText, ShimmerLine, ShimmerBadge } from '@/components/ui/shimmer';
import { ConnectionInfo } from '@/types';

interface ConnectionInspectorProps {
  providerKey: string;
  selectedConnectionId?: string;
  onConnectionSelect: (connectionId: string) => void;
}

export function ConnectionInspector({
  providerKey,
  selectedConnectionId,
  onConnectionSelect,
}: ConnectionInspectorProps) {
  const [connections, setConnections] = useState<ConnectionInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [enrichingConnections, setEnrichingConnections] = useState<Set<string>>(new Set());

  const selectedConnection = connections.find(conn => conn.id === selectedConnectionId);

  useEffect(() => {
    if (providerKey) {
      loadBasicConnections();
    }
  }, [providerKey]);

  // Reset selected connection when provider changes
  useEffect(() => {
    if (connections.length > 0 && !selectedConnectionId) {
      onConnectionSelect(connections[0].id);
    } else if (connections.length > 0 && selectedConnectionId && !connections.find(conn => conn.id === selectedConnectionId)) {
      // If the currently selected connection is not in the new list, select the first one
      onConnectionSelect(connections[0].id);
    }
  }, [connections, selectedConnectionId, onConnectionSelect]);

  useEffect(() => {
    if (selectedConnectionId && !enrichingConnections.has(selectedConnectionId)) {
      const connection = connections.find(conn => conn.id === selectedConnectionId);
      if (connection && !connection.nangoConnection && !connection.syncStatus) {
        enrichConnection(selectedConnectionId);
      }
    }
  }, [selectedConnectionId, connections]);

  const loadBasicConnections = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(
        `/api/connections/basic?providerKey=${encodeURIComponent(providerKey)}`
      );
      const data = await response.json();

      if (data.success) {
        const basicConnections = data.connections.map((conn: ConnectionInfo) => ({
          ...conn,
          enrichmentLoading: {
            nangoConnection: false,
            syncStatus: false,
            metadata: false,
          }
        }));
        setConnections(basicConnections || []);
      } else {
        setError(data.error || 'Failed to load connections');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load connections');
    } finally {
      setLoading(false);
    }
  };

  const enrichConnection = async (connectionId: string) => {
    if (enrichingConnections.has(connectionId)) return;

    setEnrichingConnections(prev => new Set(prev).add(connectionId));

    // Update loading states
    setConnections(prev => prev.map(conn =>
      conn.id === connectionId
        ? {
            ...conn,
            enrichmentLoading: {
              nangoConnection: true,
              syncStatus: true,
              metadata: true,
            }
          }
        : conn
    ));

    try {
      const response = await fetch(`/api/connections/enrich/${encodeURIComponent(connectionId)}`);
      const data = await response.json();

      if (data.success) {
        const { enrichment } = data;

        setConnections(prev => prev.map(conn => {
          if (conn.id === connectionId) {
            const enrichedConn: ConnectionInfo = {
              ...conn,
              enrichmentLoading: {
                nangoConnection: false,
                syncStatus: false,
                metadata: false,
              }
            };

            if (enrichment.nangoConnection) {
              enrichedConn.nangoConnection = enrichment.nangoConnection;
            }

            if (enrichment.syncStatus) {
              enrichedConn.syncStatus = enrichment.syncStatus;
            }

            if (enrichment.nangoMetadata) {
              enrichedConn.metadata = {
                ...enrichedConn.metadata,
                nango: enrichment.nangoMetadata,
              };
            }

            return enrichedConn;
          }
          return conn;
        }));
      } else {
        console.warn('Failed to enrich connection:', data.error);
        // Still remove loading state even if enrichment failed
        setConnections(prev => prev.map(conn =>
          conn.id === connectionId
            ? {
                ...conn,
                enrichmentLoading: {
                  nangoConnection: false,
                  syncStatus: false,
                  metadata: false,
                }
              }
            : conn
        ));
      }
    } catch (error: any) {
      console.warn('Error enriching connection:', error);
      // Remove loading state on error
      setConnections(prev => prev.map(conn =>
        conn.id === connectionId
          ? {
              ...conn,
              enrichmentLoading: {
                nangoConnection: false,
                syncStatus: false,
                metadata: false,
              }
            }
          : conn
      ));
    } finally {
      setEnrichingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getSyncStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSyncStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'success':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading connections...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button onClick={loadBasicConnections} className="mt-2" variant="outline" size="sm">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </div>
          <Badge variant="secondary">
            {connections.length} connection{connections.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {connections.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No connections found for {providerKey}</p>
          </div>
        ) : (
          <>
            {/* Connection Selector */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Connection:</label>
              <Select value={selectedConnectionId || ''} onValueChange={onConnectionSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a connection..." />
                </SelectTrigger>
                <SelectContent>
                  {connections.map(connection => (
                    <SelectItem key={connection.id} value={connection.id}>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{connection.displayName || connection.id}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Selected Connection Details */}
            {selectedConnection && (
              <div className="space-y-4">
                <Separator />

                {/* Sync Status */}
                <div className="space-y-2">
                  <h4 className="font-medium">Sync Status</h4>
                  {selectedConnection.enrichmentLoading?.syncStatus ? (
                    <ShimmerCard>
                      <div className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center space-x-2">
                          <Shimmer className="h-4 w-4" />
                          <ShimmerLine className="w-24" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <ShimmerBadge />
                          <ShimmerLine className="w-12" />
                        </div>
                      </div>
                    </ShimmerCard>
                  ) : selectedConnection.syncStatus?.syncs &&
                    selectedConnection.syncStatus.syncs.length > 0 ? (
                    <div className="space-y-2">
                      {selectedConnection.syncStatus.syncs.map((sync, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 border rounded"
                        >
                          <div className="flex items-center space-x-2">
                            {getSyncStatusIcon(sync.status)}
                            <span className="font-medium">{sync.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getSyncStatusColor(sync.status)}>
                              {sync.status}
                            </Badge>
                            {sync.frequency && (
                              <span className="text-xs text-muted-foreground">
                                {sync.frequency}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground p-2 border rounded">
                      No sync configurations found
                    </div>
                  )}
                </div>

                {/* Metadata */}
                <div className="space-y-2">
                  <h4 className="font-medium">Sync Metadata</h4>
                  {selectedConnection.enrichmentLoading?.metadata ? (
                    <div className="bg-muted p-3 rounded">
                      <ShimmerCard>
                        <ShimmerText />
                      </ShimmerCard>
                    </div>
                  ) : (
                    <div className="bg-muted p-3 rounded text-xs font-mono overflow-auto">
                      <pre>{JSON.stringify(selectedConnection.metadata || {}, null, 2)}</pre>
                    </div>
                  )}
                </div>

                {/* Errors */}
                {selectedConnection.enrichmentLoading?.nangoConnection ? (
                  <div className="space-y-2">
                    <h4 className="font-medium">Errors</h4>
                    {/* <ShimmerCard>
                      <div className="flex items-center space-x-2">
                        <Shimmer className="h-4 w-4" />
                        <ShimmerLine className="w-32" />
                        <ShimmerLine className="w-16" />
                      </div>
                    </ShimmerCard> */}
                  </div>
                ) : selectedConnection.nangoConnection?.errors &&
                  selectedConnection.nangoConnection.errors.length > 0 ? (
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Errors</h4>
                    <div className="space-y-1">
                      {selectedConnection.nangoConnection.errors.map((error, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 text-sm text-red-600"
                        >
                          <AlertCircle className="h-4 w-4" />
                          <span>{error.type}</span>
                          <span className="text-xs text-muted-foreground">({error.log_id})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : null}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
