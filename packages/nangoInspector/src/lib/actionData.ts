import { ActionDefinition, ProviderGroup, SyncDefinition, ProviderSummary } from '@/types';
import {
  ACTION_OUTPUTS as NANGO_ACTION_OUTPUTS,
  ACTION_INPUTS as NANGO_ACTION_INPUTS,
  SYNC_OUTPUTS as NANGO_SYNC_OUTPUTS,
} from '../../../emcpe-server/src/constants';

// Use the real constants from nangoIntrospection
export const ACTION_OUTPUTS: ActionDefinition[] = NANGO_ACTION_OUTPUTS.map(action => ({
  provider: action.provider,
  action: action.action!,
  model: action.model,
  description: action.description || '',
}));

export const SYNC_DEFINITIONS: SyncDefinition[] = NANGO_SYNC_OUTPUTS.map(sync => ({
  provider: sync.provider,
  sync: sync.sync!,
  model: sync.model,
  description: sync.description || '',
}));

export const ACTION_INPUTS: ActionDefinition[] = NANGO_ACTION_INPUTS.map(action => ({
  provider: action.provider,
  action: action.action!,
  model: action.model,
  description: action.description || '',
}));

export function groupSyncsByProvider(): ProviderGroup[] {
  const grouped = SYNC_DEFINITIONS.reduce(
    (acc, sync) => {
      if (!acc[sync.provider]) {
        acc[sync.provider] = [];
      }
      acc[sync.provider].push(sync as any);
      return acc;
    },
    {} as Record<string, SyncDefinition[]>
  );

  return Object.entries(grouped).map(([name, syncs]) => ({
    name,
    actions: syncs as any,
  })) as ProviderGroup[];
}

export function groupProviders(): ProviderSummary[] {
  const map: Record<string, ProviderSummary> = {};
  for (const action of ACTION_OUTPUTS) {
    if (!map[action.provider]) {
      map[action.provider] = { name: action.provider, actions: [], syncs: [] };
    }
    map[action.provider].actions.push(action);
  }
  for (const sync of SYNC_DEFINITIONS) {
    if (!map[sync.provider]) {
      map[sync.provider] = { name: sync.provider, actions: [], syncs: [] };
    }
    map[sync.provider].syncs.push(sync);
  }
  return Object.values(map).sort((a, b) => a.name.localeCompare(b.name));
}

export function groupActionsByProvider(): ProviderGroup[] {
  const grouped = ACTION_OUTPUTS.reduce(
    (acc, action) => {
      if (!acc[action.provider]) {
        acc[action.provider] = [];
      }
      acc[action.provider].push(action);
      return acc;
    },
    {} as Record<string, ActionDefinition[]>
  );

  return Object.entries(grouped)
    .map(([name, actions]) => ({ name, actions }))
    .sort((a, b) => a.name.localeCompare(b.name));
}

export function getProviderActions(provider: string): ActionDefinition[] {
  return ACTION_OUTPUTS.filter(action => action.provider === provider);
}

export function getActionDefinition(
  provider: string,
  action: string
): ActionDefinition | undefined {
  return ACTION_OUTPUTS.find(a => a.provider === provider && a.action === action);
}

export function getProviderSyncs(provider: string): SyncDefinition[] {
  return SYNC_DEFINITIONS.filter(sync => sync.provider === provider);
}

export function getSyncDefinition(provider: string, sync: string): SyncDefinition | undefined {
  return SYNC_DEFINITIONS.find(s => s.provider === provider && s.sync === sync);
}

export function getActionInputDefinition(
  provider: string,
  action: string
): ActionDefinition | undefined {
  return ACTION_INPUTS.find(a => a.provider === provider && a.action === action);
}

export async function loadSampleData(
  provider: string,
  action: string
): Promise<{ input?: any; output?: any } | null> {
  try {
    const response = await fetch(`/api/sample-data/${provider}/${action}`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn(`Failed to load sample data for ${provider}:${action}`, error);
  }
  return null;
}

export async function loadSyncData(
  provider: string,
  sync: string
): Promise<any[] | null> {
  try {
    const response = await fetch(`/api/sync-data/${provider}/${sync}`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn(`Failed to load sync data for ${provider}:${sync}`, error);
  }
  return null;
}

export async function loadScriptData(provider: string): Promise<any | null> {
  try {
    const response = await fetch(`/api/script-data/${provider}`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn(`Failed to load script data for ${provider}`, error);
  }
  return null;
}
