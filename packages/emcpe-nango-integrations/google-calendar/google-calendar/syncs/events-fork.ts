import type { NangoSync, GoogleCalendarEvent } from '../../models';

export default async function fetchData(nango: NangoSync): Promise<void> {
  try {
    // Get metadata for incremental syncing
    const metadata =
      (await nango.getMetadata<{ syncToken?: string; nextPageToken?: string }>()) || {};

    // Parameters for the Google Calendar API
    const params: Record<string, string> = {
      calendarId: 'primary',
      singleEvents: 'true',
      maxResults: '250',
    };

    // Use sync token for incremental sync if available, otherwise get events from last 30 days
    if (metadata.syncToken) {
      params['syncToken'] = metadata.syncToken;
      await nango.log('Using sync token for incremental sync', { level: 'info' });
    } else {
      // For initial sync, fetch events from 30 days ago to 1 year in the future
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

      params['timeMin'] = thirtyDaysAgo.toISOString();
      params['timeMax'] = oneYearFromNow.toISOString();
      await nango.log('Performing initial sync with time range', { level: 'info' });
    }

    let hasMorePages = true;
    let nextPageToken = metadata.nextPageToken;
    const allEvents: GoogleCalendarEvent[] = [];

    while (hasMorePages) {
      if (nextPageToken) {
        params['pageToken'] = nextPageToken;
      } else {
        delete params['pageToken'];
      }

      await nango.log(`Fetching events with params: ${JSON.stringify(params)}`, { level: 'debug' });

      const response = await nango.proxy({
        endpoint: `calendar/v3/calendars/${encodeURIComponent(
          params['calendarId'] || 'primary'
        )}/events`,
        method: 'GET',
        params,
      });

      if (response.status !== 200) {
        await nango.log(
          `Google Calendar API error: ${response.status} - ${JSON.stringify(response.data)}`,
          { level: 'error' }
        );
        throw new Error(`Google Calendar API returned status ${response.status}`);
      }

      const data = response.data;

      if (data.items && data.items.length > 0) {
        // Transform events to match our sync model
        const events: GoogleCalendarEvent[] = data.items;

        allEvents.push(...events);
        await nango.log(`Fetched ${events.length} events in this batch`, { level: 'info' });
      }

      // Check for next page
      nextPageToken = data.nextPageToken;
      hasMorePages = !!nextPageToken;

      // Store the sync token for future incremental syncs
      if (data.nextSyncToken) {
        await nango.setMetadata({ syncToken: data.nextSyncToken });
        await nango.log('Stored new sync token for next incremental sync', { level: 'info' });
      } else if (nextPageToken) {
        // Store page token if we need to continue pagination
        await nango.setMetadata({ nextPageToken });
      }
    }

    // Save all events in batch
    if (allEvents.length > 0) {
      await nango.batchSave(allEvents, 'GoogleCalendarEvent');
      await nango.log(`Successfully synced ${allEvents.length} events`, { level: 'info' });
    } else {
      await nango.log('No events to sync', { level: 'info' });
    }

    // Clear page token metadata after successful sync
    if (metadata.nextPageToken) {
      await nango.setMetadata({ syncToken: metadata.syncToken });
    }
  } catch (error: any) {
    const message =
      error.response?.data?.error?.message || error.message || 'Unknown error occurred';
    await nango.log(`Error syncing Google Calendar events: ${message}`, { level: 'error' });

    // If it's a sync token invalid error, clear it and retry without it
    if (error.response?.status === 410 || message.includes('Sync token is no longer valid')) {
      await nango.log('Sync token invalid, clearing and retrying full sync', { level: 'warn' });
      await nango.setMetadata({});
      // Don't rethrow - let it retry on next sync run
      return;
    }

    throw error;
  }
}
